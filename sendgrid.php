<?php
use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\SMTP;

require 'vendor/autoload.php';

$mail = new PHPMailer(true);

try {
    //Server settings
    $mail->isSMTP();
    $mail->Host       = 'smtp.sendgrid.net';
    $mail->SMTPAuth   = true;
    $mail->Username   = 'apikey'; // literally the string "apikey"
    $mail->Password   = '*********************************************************************'; // paste the API key here
    $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
    $mail->Port       = 587;

    //Recipients
    $mail->setFrom('<EMAIL>', 'Ilayaraja');
    $mail->addAddress('<EMAIL>', 'Ilayaraja Moback');
    $mail->addReplyTo('<EMAIL>', 'Moback');

    //Content
    $mail->isHTML(true);
    $mail->Subject = 'PHPMailer SendGrid SMTP test';
    $mail->msgHTML(file_get_contents('NZP-Hiring-Manager-Performance-email.html'), __DIR__);
    // $mail->Body    = '<h1>Hello from SendGrid + PHPMailer!</h1>';
    $mail->AltBody = 'Hello from SendGrid + PHPMailer! (plain text version)';

    $mail->send();
    echo '✅ Message has been sent';
} catch (Exception $e) {
    echo "❌ Message could not be sent. Mailer Error: {$mail->ErrorInfo}";
}

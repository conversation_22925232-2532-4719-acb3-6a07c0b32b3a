name: "Tests"

on: [push]

jobs:

  coding-standard:
    runs-on: ubuntu-18.04
    strategy:
      matrix:
        php: ['8.0']
        dependency-version: [prefer-stable]
    name: Coding standards

    steps:
      - name: Check out code
        uses: actions/checkout@v2

      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}

      - name: Install dependencies
        run: composer install --no-interaction

      - name: Check coding standards
        run: ./vendor/bin/phpcs -s

  test:
    needs: ['coding-standard']
    runs-on: ubuntu-18.04
    strategy:
      matrix:
        php: ['5.5', '5.6', '7.0.', '7.1', '7.2', '7.3', '7.4', '8.0']
        dependency-version: [prefer-stable]
    name: P${{ matrix.php }} - ${{ matrix.dependency-version }}

    steps:
      - name: Check out code
        uses: actions/checkout@v2

      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          coverage: xdebug
          ini-values: sendmail_path=/usr/sbin/sendmail -t -i, zend.multibyte=1, zend.script_encoding=UTF-8, default_charset=UTF-8
          extensions: xdebug, imap, mbstring, intl, ctype, filter, hash

      - name: Install PHP packages
        run: composer install --no-interaction

      - name: Install postfix
        run: |
          sudo apt-get install -y -qq postfix qmail
          sudo service postfix stop

      - name: Set up sendmail
        run: |
          smtp-sink -d "%d.%H.%M.%S" localhost:2500 1000 &
          mkdir -p build/logs
          sudo cp test/testbootstrap-dist.php test/testbootstrap.php
          sudo chmod +x test/fakesendmail.sh
          sudo mkdir -p /var/qmail/bin
          sudo cp test/fakesendmail.sh /var/qmail/bin/sendmail
          sudo cp test/fakesendmail.sh /usr/sbin/sendmail
      
      - name: Run tests
        run: ./vendor/bin/phpunit --configuration ./phpunit.xml.dist
